/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
const { exec } = require("child_process");
const fs = require("fs");

const ARGUMENT_SEPARATION_REGEX = /([^=\s]+)=?\s*(.*)/;
var DEST = "";
var SERVICE = "";
var SERVER_ADDRESS = "";
var DEPLOYING = false;

const APPS = {
    wiki: {
        server: "admin.envoylabs.net",
        path: "/opt/clara/resource",
        srcDir: "wiki/build/",
    },
    "admin-console": { server: "admin.envoylabs.net", path: "/opt/clara" },
    "fly-builder": { server: "builder.envoylabs.net", path: "/opt/clara" },
    "db-manager": { server: "dbm.envoylabs.net", path: "/opt/clara" },
};

start();
process.on("uncaughtException", function (err) {
    console.log(err); /* Yeeting Errors */
});

async function startMonitor() {
    fs.watch("./" + SERVICE, { recursive: true }, (eventType, filename) => {
        if (DEPLOYING) return;
        DEPLOYING = true;
        if (filename.includes("node_modules")) return;
        copyFile(
            process.env.PWD + "/" + SERVICE + "/" + filename,
            DEST + "/" + SERVICE + "/" + filename
        );
        setTimeout(() => {
            DEPLOYING = false;
        }, 100);
    });
}

async function copyFile(src, dest) {
    let cp = `rsync -avz --rsync-path='sudo rsync' --exclude '.env' "${src}" "ubuntu@${SERVER_ADDRESS}:${dest}"`;
    console.log(cp);
    try {
        executeRemote(cp);
    } catch (err) {
        //
    }
}

async function rsyncData() {
    try {
        let rsc = `rsync -avz --rsync-path='sudo rsync' --exclude '.env' "${process.env.PWD}/${SERVICE}" "ubuntu@${SERVER_ADDRESS}:${DEST}"`;
        console.log(rsc);
        console.log("Executing Sync...");
        await executeRemote(rsc, 600000);
    } catch (err) {
        //
    }
}

async function start() {
    const args = parseArgsv(process.argv);
    const argk = Object.keys(args);
    var remote = null;
    if (argk.includes("app") || argk.includes("a")) {
        remote = args["app"] || args["a"];
        if (remote == "wiki") {
            args["a"] = false;
            args["watch"] = false;
        }
        console.log("AppName: ", remote);
        if (remote === true || !remote) {
            console.error("Invalid app name...");
            showHelp();
            process.exit(1);
        }
        var ai = await getAppAddress(remote);
        console.log(ai);
        if (!ai || !ai?.server || !ai?.path) {
            console.error("Service info not found..");
            process.exit(1);
        }
        console.log("Server :", SERVER_ADDRESS);
        console.log("Syncing...");
        await rsyncData();
        console.log("Done.");
        if (args["w"] || args["watch"]) {
            startMonitor();
        }
    } else {
        showHelp();
    }
}
async function getAppAddress(appname) {
    if (!Object.keys(APPS).includes(appname)) {
        return false;
    }
    let appInfo = APPS[appname];
    DEST = appInfo.path;
    SERVICE = appInfo.srcDir || appname;
    SERVER_ADDRESS = appInfo.server;
    return appInfo;
}

async function executeRemote(cmd, timeout) {
    if (!timeout) {
        timeout = 50000;
    }
    return new Promise((resolve, reject) => {
        try {
            exec(cmd, { timeout: 50000 }, (error, stdout, stderr) => {
                if (error || stderr) {
                    console.error(stderr);
                    console.error(stderr);
                    reject(error);
                    return;
                }
                try {
                    resolve(JSON.parse(stdout));
                } catch (err) {
                    resolve(stdout);
                }
            });
        } catch (err) {
            console.error(err);
            reject(err);
        }
    });
}
function parseArgsv(argv) {
    argv = argv.slice(2);

    const parsedArgs = {};
    let argName, argValue;

    argv.forEach(function (arg) {
        arg = arg.match(ARGUMENT_SEPARATION_REGEX);
        arg.splice(0, 1);

        argName = arg[0];

        if (argName.indexOf("-") === 0) {
            argName = argName.slice(argName.slice(0, 2).lastIndexOf("-") + 1);
        }

        argValue =
            arg[1] !== ""
                ? parseFloat(arg[1]).toString() === arg[1]
                    ? +arg[1]
                    : arg[1]
                : true;

        parsedArgs[argName] = argValue;
    });
    return parsedArgs;
}

function showHelp() {
    cmds = [
        "Args:",
        "  -a --app             Application service name",
        "  -w --watch           Auto code sync",
        "  -h --help            Help",
        " example node admin_server.js -a=db-manager -w",
    ];
    cmds.forEach(function (cmd) {
        console.log(cmd);
    });
}
