{"extends": "@clara/config/tsconfig/base.json", "compilerOptions": {"paths": {"@/*": ["src/*", "src/*.ts[x]?", "src/*/index.ts[x]?"], "@site/*": ["./*"], "@components": ["src/components/index.ts"], "@components/*": ["src/components/*", "src/components/*.ts[x]?", "src/components/*/index.ts[x]?"], "@pages": ["src/pages/index.ts"], "@pages/*": ["src/pages/*", "src/pages/*.ts[x]?", "src/pages/*/index.ts[x]?"]}}, "files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}]}