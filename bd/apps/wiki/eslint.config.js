import { createUIConfig } from "@clara/config/eslint/ui.js";
import { applySecurityRules } from "@clara/config/eslint/security.js";

// Lint TypeScript projects
const projects = [
    "./tsconfig.json",
    "./tsconfig.app.json",
    "./tsconfig.node.json",
];

// Additional ignores for Docusaurus
const extraIgnores = [".docusaurus/**", "build/**", "dist/**"];

// Create UI config with security enhancements
const config = applySecurityRules(createUIConfig(projects, extraIgnores));

// Add specific rules for Docusaurus config files
config.push({
    files: ["docusaurus.config.js", "sidebars.js", "babel.config.cjs"],
    languageOptions: {
        globals: {
            module: "readonly",
            require: "readonly",
            process: "readonly",
            __dirname: "readonly",
            __filename: "readonly",
        },
    },
    rules: {
        "@typescript-eslint/no-require-imports": "off",
        "import/no-unresolved": "off",
    },
});

// Add specific rules for Docusaurus components
config.push({
    files: ["src/**/*.tsx", "src/**/*.ts"],
    rules: {
        "import/no-unresolved": "off", // Docusaurus has special module resolution
        "@typescript-eslint/no-require-imports": "off", // Allow require() in Docusaurus components
    },
});

export default config;
