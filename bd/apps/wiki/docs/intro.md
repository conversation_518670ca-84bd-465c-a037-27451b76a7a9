---
sidebar_position: 1
pagination_next: tools/Setting up SSH
---

# Pre Requisites

## Account Access
Organisation Gmail Account: this will be provided to you which will include invites for the following items
- Slack access
- Clara Code Base (gitlab).
  - [bd](https://gitlab.com/envoy-labs/bd) (Client, CURD, Rule Engine)
  - [bd-dsl](https://gitlab.com/envoy-labs/bd-dsl) (DSL, Rules)
- On fly.io Make sure you have Clara (currently Envoy labs) selected as organization in [fly.io dashboard](https://fly.io/dashboard) as shown in the following image.
![Fly Org Selection](../static/img/fly-org.png)


### What you'll need (latest versions)
- [Brew](https://brew.sh/) (Mac/Linux): Run `brew bundle` in bd repo root directory to install all dependencies below. After brew bundle is installed, restart terminal and make sure 'which pnpm' returns correct path to your Homebrew installation folder.
  - [Flyctl](https://fly.io/docs/hands-on/install-flyctl/)
  - [OrbStack](https://orbstack.dev/) instead of Docker Desktop
  - [PNPM](https://pnpm.io/installation#using-homebrew) (Preferred over npm/yarn)
- [Node.js](https://nodejs.org/en/download/) version 20 or above
  - No need to install this manually.
  - Run: 'pnpm env use --global lts' to install latest LTS node version.
  - Re-run this command to keep it updated to latest LTS version.
- [VS Code](https://code.visualstudio.com/download)
- [Wire Guard](https://www.wireguard.com/install/)

-- Before proceeding to next steps please insure all the above mentioned tools are installed. Configuring the tools will be covered in next steps