---
sidebar_position: 1
---
# Admin console configuration

This document is going to cover every step you need to do in **Admin Console** for successful deployment.


First up is naming convention, we follow specific naming convention throughout our process. For the purpose of this document we are going to cover appname. For the development machines we prefix **e** to name of the dev as appname eg eshoaib

Next go to [Admin Console](https://admin.envoylabs.net/app/envoy-labs-dashboard/customers-635ffc81a5e7e26112719e60) and Sign In using google auth - In case you don't have access ask an Admin.
After successful login you should see the following screens. 
![Add Customer admin console](../../static/img/admin-addcustomer.png) 
![Add Customer Info admin console](../../static/img/admin-addcustomerinfo.png)

Ensure following fields are filled with correct value as shown in the picture above.

```
Name = `Dev {your-name}`
App Name = `e{your-name}`
Email = `{your-email-address}`
Type = Internal
```

Now go to Envirnoments > Dev  and click Add Vars
![Add Vars](../../static/img/admin-addvars.png)
Ensure to add following value to the fields
```
VM Type = Dedicated CPU 1x
Primary Region = {closest-region}
```

Now navigate to Envirnoments > Dev > Secrets and add the following secrets

```
FLY_ID = `d-{your-appname}-nes`
```
As suggested FLY_ID is just `d-{your-appname}-nes` e.g. d-epatrick-nes

```
FLY_NES = {random-generated-32-char-string}
```
This has to be randomly 32 length long string that will be used in upcoming process. You can generate this yourself or for unix based system you can also use following command

```od -x /dev/urandom | head -1 | awk '{OFS=""; print $2$3,$4,$5,$6,$7$8$9}'```


```
DATABASE_URL = {DB-connection-string}
```
This will be provided to you by admin which is connection string of db e.g.
```postgresql://USERNAME:PASS@IP:PORT/DB```

For Reference
![Add Secrets](../../static/img/admin-addsecrets.png)