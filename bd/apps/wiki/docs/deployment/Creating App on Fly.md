---
sidebar_position: 2
pagination_next: development/dsl-wf
---
# Creating App on Fly.io
Before creating App, generate ssh keys as described [here](../tools/Setting%20up%20SSH.md), add it in your gitlab and get clone of bd and bd-dsl Repos.

By default you should have a fly.toml.template under bd directory you can copy the contents from there or
you can use contents from [this link](./Fly%20toml%20template) to create your own fly.toml file under bd directory

Next up run the command `fly launch` which should prompt you to copy the config to new app press **y** and then will ask if you want to tweak the settings press **y** here as well. Reference Image
![Reference Image](../../static/img/fly-launch.png)

This will redirect you to your browser asking you to update configuration for your fly app.
In this step ensure you have provided correctly formatted values to the App name as listed below.
```
App Name = {e}-{appname}-nes
```

**App Name** will use **FLY_ID** we set in [Admin Console](../deployment/Admin%20Console%20Configuration) which looks like `{e}-{appname}-nes`
where `e` in `{e}-{appname}-nes` has possible values of `d`, `s`, `p` and `t`
```
d = development
t = testing
s = staging
p = production
```
For the rest of the configuration you should have following selected.
```
Organization = Envoy Labs
Region = {closest-region}
Services = 8080
VM Sizes = Performance 1x
VM Memory = 2GB
```
![Reference Image](../../static/img/fly-updatetoml.png)

Leave rest of the fields blank and press Confirm. Now go back to the terminal let it validate the fly.toml file successfully after that stop the launch process (by using `CTRL + C` Don't worry this is for time being we'll fix it ASAP).
![Fly launch terminate](../../static/img/fly-launchterminate.png)

Now go to [this url](https://fly.io/dashboard/envoy-labs) make sure it has **Envoy Labs** selected as Organisation select your app from the list which looks like following
![Fly Dashboard](../../static/img/fly-secretsdashboard.png)

Navigate to secrets tab of your app on the left side bar
![Fly Apps](../../static/img/fly-secretsadd.png)

And add the secrets that we added in admin console in [this step](./Admin%20Console%20Configuration.md). After adding secrets your screen should look like this
![fly secrets](../../static/img/fly-secrets.png)

Now go back to terminal and run the following to build the image using Fly's online builder:
```
fly deploy --build-arg NODE_ENV=development --build-target=development
```

If you want to build the image locally and then upload to Fly, try:
```
fly deploy --build-arg NODE_ENV=development --build-target=development --local-only
```

For this to work, make sure your Docker Desktop Settings > Advanced > System (required password) is selected.


This will take 2-5 mins depending upon your internet connection. When asked for dedicated ipv4 or ipv6 press **n** (No). After that your application will be available at
`{fly-app-name}.fly.dev/` i.e in this case it will be `d-epatrickk-nes.fly.dev`

## Allocate v4-shared
After deploying app on fly run this command.

```
fly ips allocate-v4 --shared -a [APP_NAME]
```

## Allocate v6
After deploying app on fly run this command.

```
fly ips allocate-v6 -a [APP_NAME]
```