app = "d-clararx-nes"
primary_region = "iad"
kill_signal = "SIGINT"
kill_timeout = "5s"

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [http_service.concurrency]
    type = "requests"
    hard_limit = 1500
    soft_limit = 1000

  [[http_service.checks]]
    interval = "15s"
    timeout = "4s"
    method = "get"
    path = "/login"
    protocol = "http"
    grace_period = "30s"
    port = 8000

[[vm]]
  memory = "4gb"
  cpu_kind = "performance"
  cpus = 1