---
sidebar_position: 5
---

# Understanding Services

## Fly Apps
On fly App different services are being handled through `supervisor` you can see the status of all running apps by using following command
```
supervisorctl status
```
If you want to stop/restart/stop a running service you can following commands to performs these actions
```
supervisorctl stop nes
supervisorctl start nes
supervisorctl restart react-dev
```
Fly App services logs are stored in `/var/log/supervisor/*` directory which could be tailed using following commands
```
tail -f /var/log/supervisor/*
tail -f /var/log/supervisor/nes-std*
tail -f /var/log/supervisor/react-*

```

## Admin Servers
There are three main admin servers
1. admin.envoylabs.net (console api)
2. builder.envoylabs.net (builder api)
3. dbm.envoylabs.net (database managerment api)

On all these server service are being managed and monitored by `pm2`  please refer to pm2 docs for details [Admin Console](https://pm2.keymetrics.io/docs/usage/quick-start/)
