---
sidebar_position: 1
---

# DSL Deployment
Once application is up and running first go to `bd-dsl` directory in here first install grunt-cli globally by running
```
npm install -g grunt-cli
```
Then install node modules by running `npm install` and 
deploy the dsl using
```
grunt deploy --fqdn={FLY_ID}.fly.dev --hap=9Zg1G62lEz98XKcf --customer=xxx
```
Where FLY_ID is the one we used in previous step [Admin Console](../deployment/Admin%20Console%20Configuration)
