---
sidebar_position: 6
---

# Setting Up Apache

## Create env variables
**For Apache Machine**

Create the following env variable for Apache machine
```
SUPERSET_SECRET_KEY = {generate-random-42-characters-string}
```
This has to be randomly 42 random bytes that will be used in upcoming process. You can generate this yourself or for unix based system you can also use following command
```
openssl rand -base64 42
```
**For NES & Apache Machines**

Create the following env variables for both NES & Apache machines
```
DATABASE_URL = {same db as for env}
```
You have to set new admin username
```
SS_ADMIN_USER = {admin username}
```
Create a unique password by again running the 42 random bytes command from the above section
```
SS_ADMIN_PASS = {generate-random-42-characters-string}
```
You have to set new embed username
```
SS_EMBED_USER = {embed username}
```
Create a unique password by again running the 42 random bytes command from the above section
```
SS_EMBED_PASS = {generate-random-42-characters-string}
```

## Create a new Apache machine
Create **fly.toml** using **fly.toml.template** file and set your app name in the file. And run the following command in the same directory:
```
fly launch --no-deploy
```
Here is how you set secrets for your apache machine
```
fly secrets set SS_ADMIN_USER=${SS_ADMIN_USER} SS_ADMIN_PASS=${SS_ADMIN_PASS} SS_EMBED_USER=${SS_EMBED_USER} SS_EMBED_PASS=${SS_EMBED_PASS} SUPERSET_SECRET_KEY=${SUPERSET_SECRET_KEY} DATABASE_URL={DATABASE_URL}
```
In case there is no fly.toml file it can throw error of no App name you can pass the -a arg in this scenario
```
fly secrets set SS_ADMIN_USER=${SS_ADMIN_USER} SS_ADMIN_PASS=${SS_ADMIN_PASS} SS_EMBED_USER=${SS_EMBED_USER} SS_EMBED_PASS=${SS_EMBED_PASS} SUPERSET_SECRET_KEY=${SUPERSET_SECRET_KEY} DATABASE_URL={DATABASE_URL} -a=[APP_NAME]
```
Now, deploy your machine
```
fly deploy
```
**Set secrets for NES machine**

Here is how you set secrets for your NES machine
```
fly secrets set SS_ADMIN_USER=${SS_ADMIN_USER} SS_ADMIN_PASS=${SS_ADMIN_PASS} SS_EMBED_USER=${SS_EMBED_USER}
```
In case there is no fly.toml file it can throw error of no App name you can pass the -a arg in this scenario
```
fly secrets set SS_ADMIN_USER=${SS_ADMIN_USER} SS_ADMIN_PASS=${SS_ADMIN_PASS} SS_EMBED_USER=${SS_EMBED_USER} -a=[APP_NAME]
```

Go to the bd/nes/src/core/modules/config.js and add new variable names into the **REQUIRED_ENV_VARS** array:
    *"SS_ADMIN_USER",
    "SS_ADMIN_PASS",
    "SS_EMBED_USER"*




<!-- if you have the Homebrew package manager installed, flyctl can be installed by running:
```
brew install flyctl
```
If not, you can run the install script:
```
curl -L https://fly.io/install.sh | sh
```

## Authentication
Use you envoylabs fly account to login. **Make sure you use Envoy-labs ORGANISATION account when asked in terminal after this command**.
```
flyctl auth login
``` -->

 <!-- Your browser will open up with the Fly.io sign-in screen, enter your user name and password to sign in to complete auth step. -->
