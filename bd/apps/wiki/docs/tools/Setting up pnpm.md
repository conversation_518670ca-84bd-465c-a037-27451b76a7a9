---
sidebar_position: 2
---

# Setting Up pnpm

## Installation

You can let <PERSON><PERSON> install pnpm for you by running `brew bundle` in the bd repo root directory.

Otherwise, follow the `On POSIX systems` instructions on the
[pnpm website](https://pnpm.io/installation) to install pnpm. It should be
similar to the following but visit the website for the latest instructions.

```
curl -fsSL https://get.pnpm.io/install.sh | sh -
```

## Global setup

On OSX, you may need to install Xcode command line tools at this time. If you do
not have Xcode installed, first install it from the OSX App Store and then run the
following command:

```
xcode-select --install
```

Install the latest LTS version of Node.js:

```
pnpm env use --global lts
```

Then run the following command to install necessary packages globally:

```
pnpm add -g node-gyp oclif typescript tsx
```

## Remove old Node.js installation

It is possible that PNPM doesn't rank higher in your $PATH and running `node` calls the old version of Node.js. If this is the case, you should remove the old version of Node.js, npm, and npx:

```
which node npm npx tsx
sudo rm -rf [path to node] [path to npm] [path to npx] [path to tsx]
```

## Local update

Now that pnpm is fully installed, we can use it in our applications instead of npm or yarn.

Go to `bd` repo directory root and run the following command:

```
pnpm i -r; pnpm rb -r
```

## Updating Catalog versions

Go to any app or package folder that contains a `package.json` file and run the following command:

```
pnpm up -i --latest

# or this run from root:
pnpm up -i -r --latest

```

This will show you a list of packages that can be updated. You can then manually update the root `pnpm-workspace.yaml` file to use the latest versions of selected packages. Then run the update as per the previous step.