---
sidebar_position: 5
---

# Setting Up Flyctl
## Installation

You can let <PERSON><PERSON> install pnpm for you by running `brew bundle` in the bd repo root directory.

Otherwise, flyctl can be manually installed via `brew` by running:
```
brew install flyctl
```
If not, you can run the install script:
```
curl -L https://fly.io/install.sh | sh
```

## Authentication
Use you envoylabs fly account to login. **Make sure you use Envoy-labs ORGANISATION account when asked in terminal after this command**.
```
flyctl auth login
```

 Your browser will open up with the Fly.io sign-in screen, enter your user name and password to sign in to complete auth step.
