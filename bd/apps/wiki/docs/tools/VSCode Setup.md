---
sidebar_position: 4
---
## Setting Up VSCode
1. Install VS Code and Add Following Extension
    * ESLint
    * GitLens - Git supercharged
    * JavaScript and TypeScript Nightly
1.  Configure your eslint workdirectories in your vscode workspace settings
```
    "eslint.workingDirectories": [
        "bd/apps",
        "bd/packages",
        "bd/tshomebase",
        "bd/fly-builder",
        "bd/wiki",
        "bd/nes",
        "bd/db-manager",
        "bd/admin-console"
    ]
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
    },
    "editor.formatOnSave": true
```
1. Open VSCode Command Pallate `command + P` and restart ESLint Server
    * `> ESLint: Restart ESLint Server`