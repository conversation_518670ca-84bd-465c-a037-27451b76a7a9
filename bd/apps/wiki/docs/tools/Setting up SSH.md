---
sidebar_position: 1
---
This Document details necessary steps to configure ssh keys that will be used in gitlab.

## Configuring SSH Keys on machine
- Open command line interface
- Run Command -> `sudo -E bash`
- Create keys
    - Run command -> `ssh-keygen`
    - Note: to make it simple just press enter for the filename & passphrase
- Verify the location & keys existence
    - Run command ->  `ls ~/.ssh`
    - Path
        - `/home/<USER>/.ssh/` or `$HOME/.ssh/`
    - id_rsa   (default private key name)
    - id_rsa.pub  (default public key name)

- Change the file permission
    - Run Command -> `chmod 400 id_rsa`
- Start the ssh agent
    - Run Command -> `eval $(ssh-agent -s)`
- Add private key in ssh agent 
    - Run Command -> `ssh-add  ~/.ssh/id_rsa -k`
- Run Command -> `cat ~/.ssh/id_rsa.pub`
- **Add public key to the gitlab [Tutorial](https://www.theserverside.com/blog/Coffee-Talk-Java-News-Stories-and-Opinions/How-to-configure-GitLab-SSH-keys-for-secure-Git-connections)**

## Cloning Repos
Now you should be able to clone the repo to your machine.
Clone Both Repos
- [bd](https://gitlab.com/envoy-labs/bd) (Client, CURD, Rule Engine)
- [bd-dsl](https://gitlab.com/envoy-labs/bd-dsl) (DSL, Rules)
Repos should be cloned in such a way that you end up with flowing directory structure
```
    BnD (directory)
        |
        ------- bd
                |
                ------- nes
                |
                ------- tshomebase
                |
                .
                .
        |
        ------- bd-dsl
                |
                ------- base
                |
                ------- grunt
                |
                .
                .
``` 


## Setting Up GIT LFS

After setting up `bd` and `bd-dsl` repo, Use these commands to Set Up `Git LFS (Large File Storage)`

- If Using Homebrew: 
    Run this command in terminal. `brew install git-lfs`
- Not Using Hombrew:
         Go to [Git LFS](https://github.com/git-lfs/git-lfs)

- Run this command in `bd-dsl` directory. `git lfs install`

- Now run `git pull`
