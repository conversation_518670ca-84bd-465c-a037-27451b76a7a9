openapi: 3.0.0
info:
  title: NES API
  description: >-
    This is a sample server Petstore server.

    You can find out more about Swagger at

    [http://swagger.io](http://swagger.io) or on [irc.freenode.net,
    #swagger](http://swagger.io/irc/).

    For this sample, you can use the api key `special-key` to test the
    authorization filters.


    # Introduction

    This API is documented in **OpenAPI format** and is based on

    [Petstore sample](http://petstore.swagger.io/) provided by
    [swagger.io](http://swagger.io) team.

    It was **extended** to illustrate features of
    [generator-openapi-repo](https://github.com/Rebilly/generator-openapi-repo)

    tool and [ReDoc](https://github.com/Redocly/redoc) documentation. In
    addition to standard

    OpenAPI syntax we use a few [vendor
    extensions](https://github.com/Redocly/redoc/blob/master/docs/redoc-vendor-extensions.md).

    # Cross-Origin Resource Sharing

    This API features Cross-Origin Resource Sharing (CORS) implemented in
    compliance with  [W3C spec](https://www.w3.org/TR/cors/).

    And that allows cross-domain communication from the browser.

    All responses have a wildcard same-origin which makes them completely public
    and accessible to everyone, including any code on any site.


    # Authentication


    Console apis currently offer authentication through api key:
      - API Key
      - OAuth2
    OAuth2 - an open protocol to allow secure authorization in a simple

    and standard method from web, mobile and desktop applications.


    <SecurityDefinitions />



    Contact Support:
     Name: API Support
     Email: <EMAIL>
  version: 1.0.0
servers:
  - url: https://admin.envoylabs.net/
tags:
  - name: Config
paths:
  /service/config/nes/:
    get:
      tags:
        - Config
      summary: NES Fly Config
      x-codeSamples:
        - lang: 'cURL'
          label: 'CLI'
          source: |
            curl --request GET \
            --url "https://admin.envoylabs.net/service/config/nes/?slug=doc&fly_nes=d39ix7l0YGOsz1wtf0LQd67VX9F9xpj6&env=dev"
      security:
        - noauthAuth: []
      parameters:
        - name: slug
          in: query
          required: true
          schema:
            type: string
          example: 'doc'
        - name: fly_nes
          in: query
          required: true
          schema:
            type: string
          example: 'hIl738SpGcpyhhP'
        - name: env
          in: query
          required: true
          schema:
            type: string
            items:
              enum:
                - prod
                - staging
                - test
                - dev
              default: dev
      responses:
        '200':
          description: OK
          headers:
            Server:
              schema:
                type: number
                example: nginx/1.18.0 (Ubuntu)
            Date:
              schema:
                type: string
                example: Tue, 27 Dec 2022 13:58:58 GMT
            Content-Type:
              schema:
                type: string
                example: application/json; charset=utf-8
            Content-Length:
              schema:
                type: integer
                example: '1048'
            Connection:
              schema:
                type: string
                example: keep-alive
            etag:
              schema:
                type: string
                example: W/"418-hO1UTdhh+Vyidq/LRDI3myoAfGo"
            x-powered-by:
              schema:
                type: string
                example: Express
          content:
            application/json:
              schema:
                type: object
              example:
                name: Dev Docu
                appname: doc
                email: <EMAIL>
                created_on: '2022-11-21T17:33:39.516Z'
                contacts: []
                feature_flags: []
                slug: doc
                location: ''
                app_link: ''
                parent_corp: ''
                size: ''
                region: ''
                industry: ''
                address: null
                user_count: '999'
                s3_url: null
                login_html: null
                vm_type: null
                instance_count: null
                primary_region: null
                secondary_region: null
                env: null
                type: Demo
                secrets:
                  - id: 17
                    customer_id: 3
                    code: fly_nes
                    secret: d39ix7l0YGOsz1wtf0LQd67VX9F9xpj6
                    env: Dev
                  - id: 18
                    customer_id: 3
                    code: DATABASE_URL
                    secret: >-
                      *******************************************************************/hb?sslmode=disable
                    env: Dev
                  - id: 19
                    customer_id: 3
                    code: ENV_DB_URL
                    secret: >-
                      *******************************************************************/hb?sslmode=disable
                    env: Dev
                env_config:
                  id: 7
                  s3_url: ''
                  login_html: ''
                  vm_type: ''
                  instance_count: ''
                  env: Dev
                  customers_id: 3
                  secondary_region: ord
                  primary_region: ord
                memberships: []
        '400':
          description: Bad Request
          headers:
            Server:
              schema:
                type: number
                example: nginx/1.18.0 (Ubuntu)
            Date:
              schema:
                type: string
                example: Tue, 27 Dec 2022 13:48:58 GMT
            Content-Type:
              schema:
                type: string
                example: application/json; charset=utf-8
            Content-Length:
              schema:
                type: integer
                example: '241'
            Connection:
              schema:
                type: string
                example: keep-alive
            etag:
              schema:
                type: string
                example: W/"f1-3SGb56FknNJE0b9ZF4mm/kppbNo"
            x-powered-by:
              schema:
                type: string
                example: Express
          content:
            application/json:
              schema:
                type: object
              examples:
                example-0:
                  summary: Invalid Env Type
                  value:
                    issues:
                      - received: env
                        code: invalid_enum_value
                        options:
                          - prod
                          - staging
                          - testing
                          - dev
                        path:
                          - query
                          - env
                        message: >-
                          Invalid enum value. Expected 'prod' | 'staging' |
                          'testing' | 'dev', received 'env'
                    name: ZodError
                example-1:
                  summary: Missing Required Query Params
                  value:
                    issues:
                      - code: invalid_type
                        expected: string
                        received: undefined
                        path:
                          - query
                          - slug
                        message: Required
                      - expected: "'prod' | 'staging' | 'testing' | 'dev'"
                        received: undefined
                        code: invalid_type
                        path:
                          - query
                          - env
                        message: Required
                      - code: invalid_type
                        expected: string
                        received: undefined
                        path:
                          - query
                          - fly_nes
                        message: Required
                    name: ZodError
