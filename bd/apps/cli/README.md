# About

**Clara CLI** is a command-line interface (CLI) tool designed to simplify the process of managing and interacting with the Clara stack. It is built using [oclif](https://oclif.io/) in TypeScript and provides a user-friendly interface for various tasks, such as compiling, running dev servers, DSL management, and various development operations.

## Requirements

You need to install pnpm with oclif and typescript packages globally to use this CLI tool. Follow the [Clara Wiki](https://admin.envoylabs.net/resource/docs/tools/Setting%20up%20pnpm) for more information.

## Table of Contents

<!-- toc -->
* [About](#about)
* [Usage](#usage)
* [Commands](#commands)
<!-- tocstop -->

# Usage

<!-- usage -->
```sh-session
$ npm install -g @clara/cli
$ clara COMMAND
running command...
$ clara (--version)
@clara/cli/1.1.0 darwin-arm64 node-v22.16.0
$ clara --help [COMMAND]
USAGE
  $ clara COMMAND
...
```
<!-- usagestop -->

# Commands

<!-- commands -->
* [`clara auth`](#clara-auth)
* [`clara cache [KEY] [VALUE] [EXPIRE]`](#clara-cache-key-value-expire)
* [`clara cli [README]`](#clara-cli-readme)
* [`clara container`](#clara-container)
* [`clara container certs`](#clara-container-certs)
* [`clara container nes`](#clara-container-nes)
* [`clara container nes docker`](#clara-container-nes-docker)
* [`clara container nes fly`](#clara-container-nes-fly)
* [`clara container wiki`](#clara-container-wiki)
* [`clara container wiki docker`](#clara-container-wiki-docker)
* [`clara dsl`](#clara-dsl)
* [`clara dsl push`](#clara-dsl-push)
* [`clara dsl test`](#clara-dsl-test)
* [`clara fly deploy APP`](#clara-fly-deploy-app)
* [`clara fly watch [APP]`](#clara-fly-watch-app)

## `clara auth`

Manage credentials to access Clara stack:

```
USAGE
  $ clara auth [--json] [--force] [--dev]

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Manage credentials to access Clara stack:
  [no arguments]: Show existing auth

EXAMPLES
  $ clara auth

  $ clara auth admin

  $ clara auth fly

  $ clara auth nes
```

## `clara cache [KEY] [VALUE] [EXPIRE]`

Manage cache state

```
USAGE
  $ clara cache [KEY] [VALUE] [EXPIRE] [--json] [--force] [--dev] [-C] [-s]

ARGUMENTS
  KEY     Key to get/set
  VALUE   Value to set
  EXPIRE  Expire time in seconds

FLAGS
  -C, --clear   Clear cache
  -s, --status  Show status

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Manage cache state

EXAMPLES
  $ clara cache

  $ clara cache key1

  $ clara cache key2 val2

  $ clara cache key3 val3 3600

  $ clara cache --clear

  $ clara cache --status

FLAG DESCRIPTIONS
  -C, --clear  Clear cache

    Clear cache

  -s, --status  Show status

    Show cache status (default)
```

## `clara cli [README]`

Perform Clara CLI-related commands:

```
USAGE
  $ clara cli [README] [--json] [--force] [--dev]

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Perform Clara CLI-related commands:
  [no arguments]: Compile /README.md

EXAMPLES
  $ clara cli
  replacing <!-- usage --> in ~/bd/cli/README.md
  replacing <!-- commands --> in ~/bd/cli/README.md
  replacing <!-- toc --> in ~/bd/cli/README.md
```

## `clara container`

Manage container deployments:

```
USAGE
  $ clara container [--json] [--force] [--dev]

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Manage container deployments:
  [no arguments]: Show available container types and commands

EXAMPLES
  $ clara container

  $ clara container nes

  $ clara container wiki

  $ clara container certs
```

## `clara container certs`

Generate SSL certificates for local development using mkcert.

```
USAGE
  $ clara container certs [--json] [--force] [--dev] [--domains <value>]

FLAGS
  --domains=<value>  [default: dev.local.clararx.com,wiki.local.clararx.com] Comma-separated list of domains to generate
                     certificates for
  --force            Force regeneration of existing certificates

GLOBAL FLAGS
  --dev   Enable developer mode for debugging.
  --json  Format output as json.

DESCRIPTION
  Generate SSL certificates for local development using mkcert.

  This command creates SSL certificates for all Clara containers in a centralized location.
  Certificates are stored in packages/containers/certs/ and can be used by all containers.

EXAMPLES
  $ clara container certs

  $ clara container certs --domains dev.local.clararx.com,wiki.local.clararx.com

  $ clara container certs --force
```

## `clara container nes`

Manage NES container:

```
USAGE
  $ clara container nes [--json] [--force] [--dev]

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Manage NES container:
  docker: Launch local docker-compose stack
  fly: Deploy to fly.io

EXAMPLES
  $ clara container nes docker --app echirag --env development

  $ clara container nes fly --app p-pbx-nes --env production --local-only
```

## `clara container nes docker`

Launch local NES docker-compose stack

```
USAGE
  $ clara container nes docker --app <value> [--json] [--force] [--dev] [--env development|testing|staging|production]
    [--no-cache]

FLAGS
  --app=<value>   (required) App name (username for docker)
  --env=<option>  [default: development] Environment
                  <options: development|testing|staging|production>
  --no-cache      Build without using cache

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Launch local NES docker-compose stack

EXAMPLES
  $ clara container nes docker --app echirag --env development
```

## `clara container nes fly`

Deploy NES container to fly.io

```
USAGE
  $ clara container nes fly --app <value> [--json] [--force] [--dev] [--env development|testing|staging|production]
    [--local-only | --remote-only] [--no-cache]

FLAGS
  --app=<value>   (required) App name (app name for fly)
  --env=<option>  [default: development] Environment
                  <options: development|testing|staging|production>
  --local-only    Build locally (default)
  --no-cache      Build without using cache
  --remote-only   Build remotely

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Deploy NES container to fly.io

EXAMPLES
  $ clara container nes fly --app p-pbx-nes --env production --local-only

  $ clara container nes fly --app p-pbx-nes --env production --local-only --no-cache
```

## `clara container wiki`

Manage Wiki container:

```
USAGE
  $ clara container wiki [--json] [--force] [--dev]

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Manage Wiki container:
  docker: Launch local docker-compose stack (production build only)

EXAMPLES
  $ clara container wiki docker

  $ clara container wiki docker --no-cache
```

## `clara container wiki docker`

Launch local Wiki docker-compose stack (production build only)

```
USAGE
  $ clara container wiki docker [--json] [--force] [--dev] [--no-cache]

FLAGS
  --no-cache  Build without using cache

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Launch local Wiki docker-compose stack (production build only)

EXAMPLES
  $ clara container wiki docker

  $ clara container wiki docker --no-cache
```

## `clara dsl`

Validate & Compile DSL:

```
USAGE
  $ clara dsl [--json] [--force] [--dev] [-C] [-c <value>] [-p <value>] [-w]

FLAGS
  -C, --clear             Clear DSL cache
  -c, --customer=<value>  Specify customer
  -p, --dslpath=<value>   Specify path
  -w, --warnings          Run parser warnings checks

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Validate & Compile DSL:
  [no arguments]: Compile changed CSON

EXAMPLES
  $ clara dsl

  $ clara dsl --clear

  $ clara dsl --customer=csp

  $ clara dsl --customer=csp --clear

  $ clara dsl --warnings

FLAG DESCRIPTIONS
  -C, --clear  Clear DSL cache

    Clear DSL cache (.cson, .json files etc.)

  -c, --customer=<value>  Specify customer

    Set the customer identifier (e.g., abc)

  -p, --dslpath=<value>  Specify path

    Set the path to the DSL file

  -w, --warnings  Run parser warnings checks

    Enable running of parser warnings checks during validation
```

## `clara dsl push`

Validate, Compile, and Push DSL

```
USAGE
  $ clara dsl push -h <value> -k <value> [--json] [--force] [--dev] [-C] [-c <value>] [-f <value>] [-p <value>]

FLAGS
  -C, --clear             Clear DSL cache
  -c, --customer=<value>  Specify customer
  -f, --forms=<value>     Forms to push
  -h, --host=<value>      (required) NES Host URL
  -k, --key=<value>       (required) Specify API key
  -p, --dslpath=<value>   Specify path

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Validate, Compile, and Push DSL

EXAMPLES
  $ clara dsl push --host=xxx --key=[NES_API_KEY]

  $ clara dsl push --host=xxx --key=[NES_API_KEY] --clear

  $ clara dsl push --host=xxx --key=[NES_API_KEY] --force

  $ clara dsl push --host=xxx --key=[NES_API_KEY] --clear --force

  $ clara dsl push --forms=patient --host=xxx --key=[NES_API_KEY]

  $ clara dsl push --forms=patient,encounter --host=xxx --key=[NES_API_KEY] --force

  $ clara dsl push --forms=patient,list_us_state --host=xxx --key=[NES_API_KEY] --clear --force

FLAG DESCRIPTIONS
  -C, --clear  Clear DSL cache

    Clear DSL cache (.cson, .json files etc.)

  -c, --customer=<value>  Specify customer

    Set the customer identifier (e.g., abc)

  -f, --forms=<value>  Forms to push

    Forms to push (e.g., --forms=patient,encounter)

  -h, --host=<value>  NES Host URL

    NES URL to push DSL to

  -k, --key=<value>  Specify API key

    Set the API key for authentication

  -p, --dslpath=<value>  Specify path

    Set the path to the DSL file
```

## `clara dsl test`

Run DSL tests

```
USAGE
  $ clara dsl test [--json] [--force] [--dev]

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Run DSL tests

EXAMPLES
  $ clara dsl test
```

## `clara fly deploy APP`

Deploy latest code to given apps:

```
USAGE
  $ clara fly deploy APP [--json] [--force] [--dev] [--buildTarget <value>] [--image <value>]

ARGUMENTS
  APP  Fly.io Application Name:HAP

FLAGS
  --buildTarget=<value>  Build target to deploy
  --image=<value>        Docker Image to deploy

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Deploy latest code to given apps:
  [no arguments]: Deploy the code to app in fly.toml

EXAMPLES
  $ clara fly deploy appName:hap  --buildTarget=testing

  $ clara fly deploy d-eumer-nes:3accbc59d9ded396f7931d819b5e43f4
```

## `clara fly watch [APP]`

Watch & Sync local dev folders to Fly:

```
USAGE
  $ clara fly watch [APP] [--json] [--force] [--dev]

ARGUMENTS
  APP  Fly.io Application Name

GLOBAL FLAGS
  --dev    Enable developer mode for debugging.
  --force  Force the operation to continue after errors.
  --json   Format output as json.

DESCRIPTION
  Watch & Sync local dev folders to Fly:
  [no arguments]: Watch & Sync

EXAMPLES
  $ clara fly dev
```
<!-- commandsstop -->
