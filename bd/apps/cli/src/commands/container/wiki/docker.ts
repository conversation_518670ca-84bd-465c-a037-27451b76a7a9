import { Flags } from "@oclif/core";
import { BaseCommand } from "@core/base";

export default class WikiDocker extends BaseCommand<typeof WikiDocker> {
    static description = `Launch local Wiki docker-compose stack (production build only)`;

    static examples = [
        `$ clara container wiki docker`,
        `$ clara container wiki docker --no-cache`,
    ];

    static override flags = {
        "no-cache": Flags.boolean({
            description: "Build without using cache",
            required: false,
            default: false,
        }),
    };

    public async process(): Promise<object> {
        const { "no-cache": noCache } = this.flags;

        this.app.info(`Starting Wiki container in production mode...`);

        try {
            await this.runDocker(noCache);
            return {
                message: `Wiki container started successfully`,
                env: "production",
            };
        } catch (error) {
            this.app.fatal(`Failed to start Wiki container: ${error}`);
            return {
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }

    private async runDocker(noCache: boolean): Promise<void> {
        // Set paths
        const pkgPath = "packages/containers/wiki";
        const dockerComposePath = this.app.repoPath(
            `${pkgPath}/docker-compose.yml`
        );

        try {
            // Common docker compose parameters
            const dockerComposeParams = `docker compose -f ${dockerComposePath} --project-name "clararx"`;

            // Run docker compose
            this.app.info(
                `Starting docker compose with${noCache ? "out" : ""} cache...`
            );
            process.env.DOCKER_BUILDKIT = "1";
            process.env.BUILDKIT_PROGRESS = "plain";

            if (noCache) {
                this.app.$exec(`${dockerComposeParams} build --no-cache wiki`);
                this.app.$exec(`${dockerComposeParams} up -d wiki`);
            } else {
                this.app.$exec(`${dockerComposeParams} up --build -d wiki`);
            }

            this.app.info("Docker containers started successfully");
        } catch (error) {
            this.app.fatal(`Failed to start docker containers: ${error}`);
        }
    }
}
