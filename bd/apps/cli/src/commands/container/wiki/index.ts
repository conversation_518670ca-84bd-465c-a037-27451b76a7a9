import { BaseCommand } from "@core/base";
import { Help } from "@oclif/core";

export default class WikiIndex extends BaseCommand<typeof WikiIndex> {
    static description = `Manage Wiki container:
    docker: Launch local docker-compose stack (production build only)`;

    static examples = [
        `$ clara container wiki docker`,
        `$ clara container wiki docker --no-cache`,
    ];

    public async process(): Promise<object> {
        // Display help for the wiki command
        const help = new Help(this.config);
        await help.showCommandHelp(this.config.findCommand("container:wiki"));

        return {
            message: "Wiki container help displayed",
        };
    }
}
