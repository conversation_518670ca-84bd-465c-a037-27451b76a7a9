import { BaseCommand } from "@core/base";

export default class Container extends BaseCommand<typeof Container> {
    static description = `Manage container deployments:
    [no arguments]: Show available container types and commands`;
    static examples = [
        `$ clara container`,
        `$ clara container nes`,
        `$ clara container wiki`,
        `$ clara container certs`,
    ];

    public async process(): Promise<object> {
        this.app.info("Available container types:");
        this.app.info("  nes    - NES application container");
        this.app.info("  wiki   - Documentation (Docusaurus) container");
        this.app.info("Available container commands:");
        this.app.info(
            "  certs  - Generate SSL certificates for local development"
        );
        this.app.info("");
        this.app.info(
            "Run 'clara container [type|command] --help' for more information on a specific container type or command."
        );

        return {
            message: "Available container types and commands displayed",
            containers: ["nes", "wiki", "certs"],
        };
    }
}
