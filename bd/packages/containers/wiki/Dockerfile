# syntax=docker/dockerfile:1.7-labs

# STAGE: BASE
# Use Slim instead of <PERSON> for better compatibility
FROM node:22-slim AS base

# Set environment (DO NOT combine into a single ENV command)
ENV COREPACK_ENABLE_AUTO_PIN=0
ENV COREPACK_INTEGRITY_KEYS=0
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=production
ENV SHELL=/bin/bash

ENV CLARA_HOME="/opt/clara"
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && mkdir -p $CLARA_HOME \
    && mkdir -p $PNPM_HOME \
    && corepack enable \
    && corepack prepare pnpm@latest --activate \
    && pnpm i --no-optional --cache-dir .pnpm-cache -g --prod \
        typescript \
        tsx \
    && rm -rf .pnpm-cache

# Copy bd/ repo
WORKDIR $CLARA_HOME

# Run pnpm with just package info, without full source code
COPY ./.npmrc                     .
COPY ./tsconfig.json              .
COPY ./package.json               .
COPY ./pnpm-workspace.yaml        .
COPY apps/wiki/package.json       ./apps/wiki/package.json
COPY packages/config/package.json ./packages/config/package.json

# Install dependencies
RUN pnpm i -r --cache-dir .pnpm-cache --loglevel=debug \
    && pnpm rb -r --loglevel=debug

# Copy source code for building
COPY --exclude=package.json --exclude=*-lock.yaml --exclude=node_modules packages/config ./packages/config
COPY --exclude=package.json --exclude=*-lock.yaml --exclude=node_modules apps/wiki       ./apps/wiki

# Build wiki static files
RUN pnpm --filter @clara/wiki build \
    && rm -rf .pnpm-cache



# STAGE: PRODUCTION
FROM caddy:2-alpine AS production

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates \
    && mkdir -p /var/log/clara

# Copy built configs & static files from base stage
COPY packages/containers/wiki/config/caddy/ /opt/clara/config/caddy/
COPY packages/containers/certs/ /opt/clara/config/certs/
COPY --from=base /opt/clara/apps/wiki/dist /opt/clara/apps/wiki/dist

# Expose ports
EXPOSE 3080 3443

# Start Caddy with production configuration
CMD ["caddy", "run", "--adapter=caddyfile", "--config", "/opt/clara/config/caddy/docker.prod"]