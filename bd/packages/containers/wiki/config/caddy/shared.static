# Shared wiki configuration for production (static file serving)
# Serve static files from /resource/* baseUrl
handle_path /resource/* {
    header {
        X-HANDLER-SERVICE "Caddy"
    }
    root * /opt/clara/apps/wiki/dist
    file_server
}

# Default handler for wiki documentation in production
handle {
    header {
        X-HANDLER-SERVICE "Caddy"
    }
    root * /opt/clara/apps/wiki/dist
    file_server
}
