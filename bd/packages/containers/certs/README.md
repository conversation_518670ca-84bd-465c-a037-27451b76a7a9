# Centralized SSL Certificates for Local Development

This directory contains SSL certificates for all Clara containers running locally.

## Overview

Instead of managing certificates separately for each container, this centralized approach:
- Generates certificates once using `mkcert`
- Stores them in a shared location
- Allows all containers to reference the same certificate files
- Simplifies certificate management and rotation

## Usage

### Generate Certificates

Use the Clara CLI to generate certificates for all containers:

```bash
# Generate certificates for all known domains
clara container certs

# Generate certificates for specific domains
clara container certs --domains dev.local.clararx.com,wiki.local.clararx.com
```

### Supported Domains

- `dev.local.clararx.com` - Used by NES container
- `wiki.local.clararx.com` - Used by Wiki container

## Directory Structure

```
bd/packages/containers/certs/
├── README.md                           # This file
├── dev.local.clararx.com.pem          # Certificate for NES
├── dev.local.clararx.com-key.pem      # Private key for NES
├── wiki.local.clararx.com.pem         # Certificate for Wiki
├── wiki.local.clararx.com-key.pem     # Private key for Wiki
└── .gitignore                          # Ignore certificate files
```

## Prerequisites

- **mkcert**: Install via `brew install mkcert` or `brew bundle`
- **mkcert CA**: Run `mkcert -install` once per machine

## Container Integration

Containers copy certificates from this directory during build:

```dockerfile
# Copy certificates from centralized location
COPY packages/containers/certs/ /opt/clara/config/certs/
```

## Security Notes

- Certificate files are gitignored and not committed to the repository
- Certificates are only valid for local development
- Each developer must generate their own certificates using `mkcert`
- Certificates are automatically trusted by the local system when using `mkcert`

## Troubleshooting

### Certificate Not Found
If containers fail to start due to missing certificates:
1. Run `clara container certs` to generate certificates
2. Rebuild the container to copy the new certificates

### Certificate Not Trusted
If browsers show certificate warnings:
1. Ensure `mkcert -install` was run to install the local CA
2. Regenerate certificates with `clara container certs`
3. Restart your browser

### Permission Issues
If certificate generation fails:
1. Check that you have write permissions to this directory
2. Ensure `mkcert` is installed and in your PATH
